package services

import (
	"errors"
	"testing"

	"ops-api/internal/core/domain"
	"ops-api/internal/core/ports"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// Mock repositories
type MockServiceRepository struct {
	mock.Mock
}

func (m *MockServiceRepository) Insert(service *domain.Service) error {
	args := m.Called(service)
	return args.Error(0)
}

func (m *MockServiceRepository) FindAll(filter *ports.ServiceFilter) ([]*domain.Service, error) {
	args := m.Called(filter)
	return args.Get(0).([]*domain.Service), args.Error(1)
}

func (m *MockServiceRepository) FindByID(id uint64) (*domain.Service, error) {
	args := m.Called(id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*domain.Service), args.Error(1)
}

func (m *MockServiceRepository) Update(service *domain.Service) error {
	args := m.Called(service)
	return args.Error(0)
}

func (m *MockServiceRepository) Delete(id uint64) error {
	args := m.Called(id)
	return args.Error(0)
}

type MockNamespaceRepository struct {
	mock.Mock
}

func (m *MockNamespaceRepository) FindByID(id uint64) (*domain.Namespace, error) {
	args := m.Called(id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*domain.Namespace), args.Error(1)
}

func (m *MockNamespaceRepository) Insert(namespace *domain.Namespace) error {
	args := m.Called(namespace)
	return args.Error(0)
}

func (m *MockNamespaceRepository) FindAll(filter *ports.NamespaceFilter) ([]*domain.Namespace, error) {
	args := m.Called(filter)
	return args.Get(0).([]*domain.Namespace), args.Error(1)
}

func (m *MockNamespaceRepository) FindAllByUserWorkspaces(userID uint64, filter *ports.NamespaceFilter) ([]*domain.Namespace, error) {
	args := m.Called(userID, filter)
	return args.Get(0).([]*domain.Namespace), args.Error(1)
}

func (m *MockNamespaceRepository) Update(namespace *domain.Namespace) error {
	args := m.Called(namespace)
	return args.Error(0)
}

func (m *MockNamespaceRepository) Delete(id uint64) error {
	args := m.Called(id)
	return args.Error(0)
}

func (m *MockNamespaceRepository) CountByType(namespaceType domain.NamespaceType) (int64, error) {
	args := m.Called(namespaceType)
	return args.Get(0).(int64), args.Error(1)
}

func TestServiceService_Create(t *testing.T) {
	tests := []struct {
		name           string
		serviceName    string
		port           string
		targetPort     string
		serviceType    string
		clusterIP      string
		externalIP     string
		isActive       bool
		namespaceID    uint64
		deploymentID   uint64
		setupMocks     func(*MockServiceRepository, *MockNamespaceRepository)
		expectedError  string
		expectedResult bool
	}{
		{
			name:         "successful creation with active service",
			serviceName:  "test-service",
			port:         "8080",
			targetPort:   "8080",
			serviceType:  "ClusterIP",
			clusterIP:    "",
			externalIP:   "",
			isActive:     true,
			namespaceID:  1,
			deploymentID: 1,
			setupMocks: func(serviceRepo *MockServiceRepository, namespaceRepo *MockNamespaceRepository) {
				namespace := &domain.Namespace{BaseModel: domain.BaseModel{ID: 1}}
				namespaceRepo.On("FindByID", uint64(1)).Return(namespace, nil)

				// Mock Insert to set the ID on the service
				serviceRepo.On("Insert", mock.AnythingOfType("*domain.Service")).Run(func(args mock.Arguments) {
					service := args.Get(0).(*domain.Service)
					service.ID = 1 // Simulate database setting the ID
				}).Return(nil)

				createdService := &domain.Service{
					BaseModel:    domain.BaseModel{ID: 1},
					Name:         "test-service",
					Port:         "8080",
					TargetPort:   "8080",
					Type:         "ClusterIP",
					IsActive:     true,
					NamespaceID:  1,
					DeploymentID: 1,
					StatusID:     1,
				}
				serviceRepo.On("FindByID", uint64(1)).Return(createdService, nil)
			},
			expectedResult: true,
		},
		{
			name:         "successful creation with inactive service",
			serviceName:  "test-service",
			port:         "8080",
			targetPort:   "8080",
			serviceType:  "ClusterIP",
			clusterIP:    "",
			externalIP:   "",
			isActive:     false,
			namespaceID:  1,
			deploymentID: 1,
			setupMocks: func(serviceRepo *MockServiceRepository, namespaceRepo *MockNamespaceRepository) {
				namespace := &domain.Namespace{BaseModel: domain.BaseModel{ID: 1}}
				namespaceRepo.On("FindByID", uint64(1)).Return(namespace, nil)

				// Mock Insert to set the ID on the service
				serviceRepo.On("Insert", mock.AnythingOfType("*domain.Service")).Run(func(args mock.Arguments) {
					service := args.Get(0).(*domain.Service)
					service.ID = 1 // Simulate database setting the ID
				}).Return(nil)

				createdService := &domain.Service{
					BaseModel:    domain.BaseModel{ID: 1},
					Name:         "test-service",
					Port:         "8080",
					TargetPort:   "8080",
					Type:         "ClusterIP",
					IsActive:     false,
					NamespaceID:  1,
					DeploymentID: 1,
					StatusID:     1,
				}
				serviceRepo.On("FindByID", uint64(1)).Return(createdService, nil)
			},
			expectedResult: true,
		},
		{
			name:          "empty name error",
			serviceName:   "",
			port:          "8080",
			targetPort:    "8080",
			serviceType:   "ClusterIP",
			isActive:      true,
			namespaceID:   1,
			deploymentID:  1,
			setupMocks:    func(serviceRepo *MockServiceRepository, namespaceRepo *MockNamespaceRepository) {},
			expectedError: "name is required",
		},
		{
			name:          "empty port error",
			serviceName:   "test-service",
			port:          "",
			targetPort:    "8080",
			serviceType:   "ClusterIP",
			isActive:      true,
			namespaceID:   1,
			deploymentID:  1,
			setupMocks:    func(serviceRepo *MockServiceRepository, namespaceRepo *MockNamespaceRepository) {},
			expectedError: "port is required",
		},
		{
			name:          "namespace not found error",
			serviceName:   "test-service",
			port:          "8080",
			targetPort:    "8080",
			serviceType:   "ClusterIP",
			isActive:      true,
			namespaceID:   1,
			deploymentID:  1,
			setupMocks: func(serviceRepo *MockServiceRepository, namespaceRepo *MockNamespaceRepository) {
				namespaceRepo.On("FindByID", uint64(1)).Return(nil, errors.New("not found"))
			},
			expectedError: "namespace not found",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			serviceRepo := new(MockServiceRepository)
			namespaceRepo := new(MockNamespaceRepository)
			
			tt.setupMocks(serviceRepo, namespaceRepo)
			
			service := NewServiceService(serviceRepo, namespaceRepo)
			
			result, err := service.Create(
				tt.serviceName,
				tt.port,
				tt.targetPort,
				tt.serviceType,
				tt.clusterIP,
				tt.externalIP,
				tt.isActive,
				tt.namespaceID,
				tt.deploymentID,
			)
			
			if tt.expectedError != "" {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.isActive, result.IsActive)
			}
			
			serviceRepo.AssertExpectations(t)
			namespaceRepo.AssertExpectations(t)
		})
	}
}

func TestServiceService_GetAll(t *testing.T) {
	tests := []struct {
		name           string
		filter         *ports.ServiceFilter
		setupMocks     func(*MockServiceRepository)
		expectedCount  int
		expectedError  string
	}{
		{
			name:   "get all services without filter",
			filter: nil,
			setupMocks: func(serviceRepo *MockServiceRepository) {
				services := []*domain.Service{
					{BaseModel: domain.BaseModel{ID: 1}, Name: "service1", IsActive: true},
					{BaseModel: domain.BaseModel{ID: 2}, Name: "service2", IsActive: false},
				}
				serviceRepo.On("FindAll", (*ports.ServiceFilter)(nil)).Return(services, nil)
			},
			expectedCount: 2,
		},
		{
			name: "get active services only",
			filter: &ports.ServiceFilter{
				IsActive: func() *bool { b := true; return &b }(),
			},
			setupMocks: func(serviceRepo *MockServiceRepository) {
				services := []*domain.Service{
					{BaseModel: domain.BaseModel{ID: 1}, Name: "service1", IsActive: true},
				}
				serviceRepo.On("FindAll", mock.AnythingOfType("*ports.ServiceFilter")).Return(services, nil)
			},
			expectedCount: 1,
		},
		{
			name: "get inactive services only",
			filter: &ports.ServiceFilter{
				IsActive: func() *bool { b := false; return &b }(),
			},
			setupMocks: func(serviceRepo *MockServiceRepository) {
				services := []*domain.Service{
					{BaseModel: domain.BaseModel{ID: 2}, Name: "service2", IsActive: false},
				}
				serviceRepo.On("FindAll", mock.AnythingOfType("*ports.ServiceFilter")).Return(services, nil)
			},
			expectedCount: 1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			serviceRepo := new(MockServiceRepository)
			namespaceRepo := new(MockNamespaceRepository)

			tt.setupMocks(serviceRepo)

			service := NewServiceService(serviceRepo, namespaceRepo)

			result, err := service.GetAll(tt.filter)

			if tt.expectedError != "" {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError)
			} else {
				assert.NoError(t, err)
				assert.Len(t, result, tt.expectedCount)
			}

			serviceRepo.AssertExpectations(t)
		})
	}
}

func TestServiceService_UpdateActiveStatus(t *testing.T) {
	tests := []struct {
		name          string
		serviceID     uint64
		isActive      bool
		setupMocks    func(*MockServiceRepository)
		expectedError string
	}{
		{
			name:      "successful update to active",
			serviceID: 1,
			isActive:  true,
			setupMocks: func(serviceRepo *MockServiceRepository) {
				existingService := &domain.Service{
					BaseModel: domain.BaseModel{ID: 1},
					Name:      "test-service",
					IsActive:  false,
				}
				serviceRepo.On("FindByID", uint64(1)).Return(existingService, nil)
				serviceRepo.On("Update", mock.AnythingOfType("*domain.Service")).Return(nil)
			},
		},
		{
			name:      "successful update to inactive",
			serviceID: 1,
			isActive:  false,
			setupMocks: func(serviceRepo *MockServiceRepository) {
				existingService := &domain.Service{
					BaseModel: domain.BaseModel{ID: 1},
					Name:      "test-service",
					IsActive:  true,
				}
				serviceRepo.On("FindByID", uint64(1)).Return(existingService, nil)
				serviceRepo.On("Update", mock.AnythingOfType("*domain.Service")).Return(nil)
			},
		},
		{
			name:      "service not found error",
			serviceID: 999,
			isActive:  true,
			setupMocks: func(serviceRepo *MockServiceRepository) {
				serviceRepo.On("FindByID", uint64(999)).Return(nil, errors.New("not found"))
			},
			expectedError: "service not found",
		},
		{
			name:      "zero ID error",
			serviceID: 0,
			isActive:  true,
			setupMocks: func(serviceRepo *MockServiceRepository) {
				// No mocks needed as validation happens before repository call
			},
			expectedError: "id is required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			serviceRepo := new(MockServiceRepository)
			namespaceRepo := new(MockNamespaceRepository)

			tt.setupMocks(serviceRepo)

			service := NewServiceService(serviceRepo, namespaceRepo)

			result, err := service.UpdateActiveStatus(tt.serviceID, tt.isActive)

			if tt.expectedError != "" {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.isActive, result.IsActive)
			}

			serviceRepo.AssertExpectations(t)
		})
	}
}
