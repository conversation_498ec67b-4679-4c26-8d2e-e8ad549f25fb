package handlers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"ops-api/internal/core/domain"
	"ops-api/internal/core/dto"
	"ops-api/internal/core/ports"

	"github.com/gofiber/fiber/v2"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// Mock services
type MockServiceService struct {
	mock.Mock
}

func (m *MockServiceService) Create(name, port, targetPort, serviceType, clusterIp, ExternalIp string, isActive bool, namespaceID, deploymentID uint64) (*domain.Service, error) {
	args := m.Called(name, port, targetPort, serviceType, clusterIp, ExternalIp, isActive, namespaceID, deploymentID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*domain.Service), args.Error(1)
}

func (m *MockServiceService) GetAll(filter *ports.ServiceFilter) ([]*domain.Service, error) {
	args := m.Called(filter)
	return args.Get(0).([]*domain.Service), args.Error(1)
}

func (m *MockServiceService) GetByID(id uint64) (*domain.Service, error) {
	args := m.Called(id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*domain.Service), args.Error(1)
}

func (m *MockServiceService) Update(id uint64, name, port, targetPort, serviceType, clusterIp, ExternalIp string, isActive bool, namespaceID, deploymentID, statusID uint64) (*domain.Service, error) {
	args := m.Called(id, name, port, targetPort, serviceType, clusterIp, ExternalIp, isActive, namespaceID, deploymentID, statusID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*domain.Service), args.Error(1)
}

func (m *MockServiceService) UpdateStatus(id uint64, statusID uint64) (*domain.Service, error) {
	args := m.Called(id, statusID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*domain.Service), args.Error(1)
}

func (m *MockServiceService) UpdateActiveStatus(id uint64, isActive bool) (*domain.Service, error) {
	args := m.Called(id, isActive)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*domain.Service), args.Error(1)
}

func (m *MockServiceService) Delete(id uint64) error {
	args := m.Called(id)
	return args.Error(0)
}

type MockDomainService struct {
	mock.Mock
}

func (m *MockDomainService) GetDefaultByNamespaceID(namespaceID uint64) (*domain.Domain, error) {
	args := m.Called(namespaceID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*domain.Domain), args.Error(1)
}

// Add other required methods as stubs
func (m *MockDomainService) Create(name string, isDefault, isActive bool, zoneID, accountID, accountName string, namespaceID uint64) (*domain.Domain, error) {
	args := m.Called(name, isDefault, isActive, zoneID, accountID, accountName, namespaceID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*domain.Domain), args.Error(1)
}

func (m *MockDomainService) GetAll(filter *ports.DomainFilter) ([]*domain.Domain, error) {
	args := m.Called(filter)
	return args.Get(0).([]*domain.Domain), args.Error(1)
}

func (m *MockDomainService) GetAllWithPagination(filter *ports.DomainFilter) ([]*domain.Domain, uint64, error) {
	args := m.Called(filter)
	return args.Get(0).([]*domain.Domain), args.Get(1).(uint64), args.Error(2)
}

func (m *MockDomainService) GetByID(id uint64) (*domain.Domain, error) {
	args := m.Called(id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*domain.Domain), args.Error(1)
}

func (m *MockDomainService) Update(id uint64, name string, isDefault, isActive bool, zoneID, accountID, accountName string, namespaceID uint64, index int) (*domain.Domain, error) {
	args := m.Called(id, name, isDefault, isActive, zoneID, accountID, accountName, namespaceID, index)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*domain.Domain), args.Error(1)
}

func (m *MockDomainService) UpdateStatus(id uint64, isActive bool) (*domain.Domain, error) {
	args := m.Called(id, isActive)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*domain.Domain), args.Error(1)
}

func (m *MockDomainService) SetDefault(userID, id uint64, accessToken string, isRedirect bool) (*domain.Domain, error) {
	args := m.Called(userID, id, accessToken, isRedirect)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*domain.Domain), args.Error(1)
}

func (m *MockDomainService) Delete(id uint64) error {
	args := m.Called(id)
	return args.Error(0)
}

func (m *MockDomainService) DeleteAllByNamespaceID(namespaceID uint64) error {
	args := m.Called(namespaceID)
	return args.Error(0)
}

func (m *MockDomainService) GetUserIDFromNamespaceID(namespaceID uint64) (uint64, error) {
	args := m.Called(namespaceID)
	return args.Get(0).(uint64), args.Error(1)
}

type MockNamespaceService struct {
	mock.Mock
}

func (m *MockNamespaceService) GetByID(id uint64) (*domain.Namespace, error) {
	args := m.Called(id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*domain.Namespace), args.Error(1)
}

// Add other required methods as stubs
func (m *MockNamespaceService) Create(name, slug string, isActive bool, Type domain.NamespaceType, clusterID uint64) (*domain.Namespace, error) {
	args := m.Called(name, slug, isActive, Type, clusterID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*domain.Namespace), args.Error(1)
}

func (m *MockNamespaceService) GetAll(filter *ports.NamespaceFilter) ([]*domain.Namespace, error) {
	args := m.Called(filter)
	return args.Get(0).([]*domain.Namespace), args.Error(1)
}

func (m *MockNamespaceService) GetAllByUserWorkspaces(userID uint64, filter *ports.NamespaceFilter) ([]*domain.Namespace, error) {
	args := m.Called(userID, filter)
	return args.Get(0).([]*domain.Namespace), args.Error(1)
}

func (m *MockNamespaceService) Update(id uint64, name, slug string, isActive bool, Type domain.NamespaceType, clusterID uint64) (*domain.Namespace, error) {
	args := m.Called(id, name, slug, isActive, Type, clusterID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*domain.Namespace), args.Error(1)
}

func (m *MockNamespaceService) Delete(id uint64) error {
	args := m.Called(id)
	return args.Error(0)
}

func TestServiceHandler_CreateService(t *testing.T) {
	tests := []struct {
		name           string
		requestBody    dto.CreateServiceRequest
		setupMocks     func(*MockServiceService)
		expectedStatus int
		expectedError  bool
	}{
		{
			name: "successful creation with is_active true",
			requestBody: dto.CreateServiceRequest{
				Name:         "test-service",
				Port:         "8080",
				TargetPort:   "8080",
				Type:         "ClusterIP",
				IsActive:     func() *bool { b := true; return &b }(),
				NamespaceID:  1,
				DeploymentID: 1,
			},
			setupMocks: func(serviceService *MockServiceService) {
				createdService := &domain.Service{
					BaseModel:    domain.BaseModel{ID: 1},
					Name:         "test-service",
					Port:         "8080",
					TargetPort:   "8080",
					Type:         "ClusterIP",
					IsActive:     true,
					NamespaceID:  1,
					DeploymentID: 1,
					StatusID:     1,
				}
				serviceService.On("Create", "test-service", "8080", "8080", "ClusterIP", "", "", true, uint64(1), uint64(1)).Return(createdService, nil)
			},
			expectedStatus: http.StatusCreated,
			expectedError:  false,
		},
		{
			name: "successful creation with is_active false",
			requestBody: dto.CreateServiceRequest{
				Name:         "test-service",
				Port:         "8080",
				TargetPort:   "8080",
				Type:         "ClusterIP",
				IsActive:     func() *bool { b := false; return &b }(),
				NamespaceID:  1,
				DeploymentID: 1,
			},
			setupMocks: func(serviceService *MockServiceService) {
				createdService := &domain.Service{
					BaseModel:    domain.BaseModel{ID: 1},
					Name:         "test-service",
					Port:         "8080",
					TargetPort:   "8080",
					Type:         "ClusterIP",
					IsActive:     false,
					NamespaceID:  1,
					DeploymentID: 1,
					StatusID:     1,
				}
				serviceService.On("Create", "test-service", "8080", "8080", "ClusterIP", "", "", false, uint64(1), uint64(1)).Return(createdService, nil)
			},
			expectedStatus: http.StatusCreated,
			expectedError:  false,
		},
		{
			name: "successful creation with is_active nil (defaults to true)",
			requestBody: dto.CreateServiceRequest{
				Name:         "test-service",
				Port:         "8080",
				TargetPort:   "8080",
				Type:         "ClusterIP",
				IsActive:     nil, // Should default to true
				NamespaceID:  1,
				DeploymentID: 1,
			},
			setupMocks: func(serviceService *MockServiceService) {
				createdService := &domain.Service{
					BaseModel:    domain.BaseModel{ID: 1},
					Name:         "test-service",
					Port:         "8080",
					TargetPort:   "8080",
					Type:         "ClusterIP",
					IsActive:     true,
					NamespaceID:  1,
					DeploymentID: 1,
					StatusID:     1,
				}
				serviceService.On("Create", "test-service", "8080", "8080", "ClusterIP", "", "", true, uint64(1), uint64(1)).Return(createdService, nil)
			},
			expectedStatus: http.StatusCreated,
			expectedError:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			app := fiber.New()
			serviceService := new(MockServiceService)
			domainService := new(MockDomainService)
			namespaceService := new(MockNamespaceService)
			
			tt.setupMocks(serviceService)
			
			handler := NewServiceHandler(serviceService, domainService, namespaceService)
			app.Post("/services", handler.CreateService)

			// Create request
			body, _ := json.Marshal(tt.requestBody)
			req := httptest.NewRequest("POST", "/services", bytes.NewReader(body))
			req.Header.Set("Content-Type", "application/json")

			// Execute
			resp, err := app.Test(req)
			assert.NoError(t, err)

			// Assert
			assert.Equal(t, tt.expectedStatus, resp.StatusCode)
			
			serviceService.AssertExpectations(t)
		})
	}
}

func TestServiceHandler_GetServices_WithIsActiveFilter(t *testing.T) {
	tests := []struct {
		name           string
		queryParams    string
		setupMocks     func(*MockServiceService)
		expectedStatus int
		expectedCount  int
	}{
		{
			name:        "get active services only",
			queryParams: "?is_active=true",
			setupMocks: func(serviceService *MockServiceService) {
				services := []*domain.Service{
					{BaseModel: domain.BaseModel{ID: 1}, Name: "active-service", IsActive: true},
				}
				serviceService.On("GetAll", mock.MatchedBy(func(filter *ports.ServiceFilter) bool {
					return filter != nil && filter.IsActive != nil && *filter.IsActive == true
				})).Return(services, nil)
			},
			expectedStatus: http.StatusOK,
			expectedCount:  1,
		},
		{
			name:        "get inactive services only",
			queryParams: "?is_active=false",
			setupMocks: func(serviceService *MockServiceService) {
				services := []*domain.Service{
					{BaseModel: domain.BaseModel{ID: 2}, Name: "inactive-service", IsActive: false},
				}
				serviceService.On("GetAll", mock.MatchedBy(func(filter *ports.ServiceFilter) bool {
					return filter != nil && filter.IsActive != nil && *filter.IsActive == false
				})).Return(services, nil)
			},
			expectedStatus: http.StatusOK,
			expectedCount:  1,
		},
		{
			name:        "get all services without filter",
			queryParams: "",
			setupMocks: func(serviceService *MockServiceService) {
				services := []*domain.Service{
					{BaseModel: domain.BaseModel{ID: 1}, Name: "active-service", IsActive: true},
					{BaseModel: domain.BaseModel{ID: 2}, Name: "inactive-service", IsActive: false},
				}
				serviceService.On("GetAll", mock.MatchedBy(func(filter *ports.ServiceFilter) bool {
					return filter != nil && filter.IsActive == nil
				})).Return(services, nil)
			},
			expectedStatus: http.StatusOK,
			expectedCount:  2,
		},
		{
			name:        "invalid is_active parameter",
			queryParams: "?is_active=invalid",
			setupMocks:  func(serviceService *MockServiceService) {},
			expectedStatus: http.StatusBadRequest,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			app := fiber.New()
			serviceService := new(MockServiceService)
			domainService := new(MockDomainService)
			namespaceService := new(MockNamespaceService)

			tt.setupMocks(serviceService)

			handler := NewServiceHandler(serviceService, domainService, namespaceService)
			app.Get("/services", handler.GetServices)

			// Create request
			req := httptest.NewRequest("GET", "/services"+tt.queryParams, nil)

			// Execute
			resp, err := app.Test(req)
			assert.NoError(t, err)

			// Assert
			assert.Equal(t, tt.expectedStatus, resp.StatusCode)

			if tt.expectedStatus == http.StatusOK {
				var response map[string]interface{}
				json.NewDecoder(resp.Body).Decode(&response)

				data := response["data"].([]interface{})
				assert.Len(t, data, tt.expectedCount)
			}

			serviceService.AssertExpectations(t)
		})
	}
}

func TestServiceHandler_UpdateServiceActiveStatus(t *testing.T) {
	tests := []struct {
		name           string
		serviceID      string
		requestBody    map[string]interface{}
		setupMocks     func(*MockServiceService)
		expectedStatus int
	}{
		{
			name:      "successful update to active",
			serviceID: "1",
			requestBody: map[string]interface{}{
				"is_active": true,
			},
			setupMocks: func(serviceService *MockServiceService) {
				updatedService := &domain.Service{
					BaseModel: domain.BaseModel{ID: 1},
					Name:      "test-service",
					IsActive:  true,
				}
				serviceService.On("UpdateActiveStatus", uint64(1), true).Return(updatedService, nil)
			},
			expectedStatus: http.StatusOK,
		},
		{
			name:      "successful update to inactive",
			serviceID: "1",
			requestBody: map[string]interface{}{
				"is_active": false,
			},
			setupMocks: func(serviceService *MockServiceService) {
				updatedService := &domain.Service{
					BaseModel: domain.BaseModel{ID: 1},
					Name:      "test-service",
					IsActive:  false,
				}
				serviceService.On("UpdateActiveStatus", uint64(1), false).Return(updatedService, nil)
			},
			expectedStatus: http.StatusOK,
		},
		{
			name:      "invalid service ID",
			serviceID: "invalid",
			requestBody: map[string]interface{}{
				"is_active": true,
			},
			setupMocks:     func(serviceService *MockServiceService) {},
			expectedStatus: http.StatusBadRequest,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			app := fiber.New()
			serviceService := new(MockServiceService)
			domainService := new(MockDomainService)
			namespaceService := new(MockNamespaceService)

			tt.setupMocks(serviceService)

			handler := NewServiceHandler(serviceService, domainService, namespaceService)
			app.Patch("/services/:id/active", handler.UpdateServiceActiveStatus)

			// Create request
			body, _ := json.Marshal(tt.requestBody)
			req := httptest.NewRequest("PATCH", "/services/"+tt.serviceID+"/active", bytes.NewReader(body))
			req.Header.Set("Content-Type", "application/json")

			// Execute
			resp, err := app.Test(req)
			assert.NoError(t, err)

			// Assert
			assert.Equal(t, tt.expectedStatus, resp.StatusCode)

			serviceService.AssertExpectations(t)
		})
	}
}
