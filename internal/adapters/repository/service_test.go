package repository

import (
	"testing"

	"ops-api/internal/core/domain"
	"ops-api/internal/core/ports"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

type ServiceRepositoryTestSuite struct {
	suite.Suite
	db   *gorm.DB
	repo ports.ServiceRepository
}

func (suite *ServiceRepositoryTestSuite) SetupSuite() {
	// Create in-memory SQLite database for testing
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	suite.Require().NoError(err)

	// Auto-migrate the schema
	err = db.AutoMigrate(&domain.Service{}, &domain.Namespace{}, &domain.Deployment{}, &domain.ServerStatus{})
	suite.Require().NoError(err)

	suite.db = db
	suite.repo = NewServiceRepository(db)
}

func (suite *ServiceRepositoryTestSuite) SetupTest() {
	// Clean up data before each test
	suite.db.Exec("DELETE FROM services")
	suite.db.Exec("DELETE FROM namespaces")
	suite.db.Exec("DELETE FROM deployments")
	suite.db.Exec("DELETE FROM server_statuses")
}

func (suite *ServiceRepositoryTestSuite) TestInsert() {
	service := &domain.Service{
		Name:         "test-service",
		Port:         "8080",
		TargetPort:   "8080",
		Type:         "ClusterIP",
		IsActive:     true,
		NamespaceID:  1,
		DeploymentID: 1,
		StatusID:     1,
	}

	err := suite.repo.Insert(service)
	
	assert.NoError(suite.T(), err)
	assert.NotZero(suite.T(), service.ID)
	
	// Verify the service was inserted with correct is_active value
	var insertedService domain.Service
	err = suite.db.First(&insertedService, service.ID).Error
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), true, insertedService.IsActive)
	assert.Equal(suite.T(), "test-service", insertedService.Name)
}

func (suite *ServiceRepositoryTestSuite) TestFindAll_WithIsActiveFilter() {
	// Insert test data
	activeService := &domain.Service{
		Name:         "active-service",
		Port:         "8080",
		TargetPort:   "8080",
		Type:         "ClusterIP",
		IsActive:     true,
		NamespaceID:  1,
		DeploymentID: 1,
		StatusID:     1,
	}
	
	inactiveService := &domain.Service{
		Name:         "inactive-service",
		Port:         "8081",
		TargetPort:   "8081",
		Type:         "ClusterIP",
		IsActive:     false,
		NamespaceID:  1,
		DeploymentID: 1,
		StatusID:     1,
	}

	err := suite.repo.Insert(activeService)
	suite.Require().NoError(err)
	
	err = suite.repo.Insert(inactiveService)
	suite.Require().NoError(err)

	// Test filter for active services only
	activeFilter := &ports.ServiceFilter{
		IsActive: func() *bool { b := true; return &b }(),
	}
	
	activeServices, err := suite.repo.FindAll(activeFilter)
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), activeServices, 1)
	assert.Equal(suite.T(), "active-service", activeServices[0].Name)
	assert.True(suite.T(), activeServices[0].IsActive)

	// Test filter for inactive services only
	inactiveFilter := &ports.ServiceFilter{
		IsActive: func() *bool { b := false; return &b }(),
	}
	
	inactiveServices, err := suite.repo.FindAll(inactiveFilter)
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), inactiveServices, 1)
	assert.Equal(suite.T(), "inactive-service", inactiveServices[0].Name)
	assert.False(suite.T(), inactiveServices[0].IsActive)

	// Test no filter (should return all services)
	allServices, err := suite.repo.FindAll(nil)
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), allServices, 2)
}

func (suite *ServiceRepositoryTestSuite) TestFindAll_WithCombinedFilters() {
	// Insert test data
	services := []*domain.Service{
		{
			Name:         "web-service",
			Port:         "8080",
			TargetPort:   "8080",
			Type:         "ClusterIP",
			IsActive:     true,
			NamespaceID:  1,
			DeploymentID: 1,
			StatusID:     1,
		},
		{
			Name:         "api-service",
			Port:         "8081",
			TargetPort:   "8081",
			Type:         "ClusterIP",
			IsActive:     false,
			NamespaceID:  1,
			DeploymentID: 1,
			StatusID:     1,
		},
		{
			Name:         "web-service",
			Port:         "8082",
			TargetPort:   "8082",
			Type:         "ClusterIP",
			IsActive:     true,
			NamespaceID:  2,
			DeploymentID: 2,
			StatusID:     1,
		},
	}

	for _, service := range services {
		err := suite.repo.Insert(service)
		suite.Require().NoError(err)
	}

	// Test combined filter: name contains "web" AND is_active = true
	filter := &ports.ServiceFilter{
		Name:     func() *string { s := "web"; return &s }(),
		IsActive: func() *bool { b := true; return &b }(),
	}
	
	filteredServices, err := suite.repo.FindAll(filter)
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), filteredServices, 2) // Both web services are active
	
	for _, service := range filteredServices {
		assert.Contains(suite.T(), service.Name, "web")
		assert.True(suite.T(), service.IsActive)
	}
}

func (suite *ServiceRepositoryTestSuite) TestUpdate_IsActiveField() {
	// Insert a service
	service := &domain.Service{
		Name:         "test-service",
		Port:         "8080",
		TargetPort:   "8080",
		Type:         "ClusterIP",
		IsActive:     true,
		NamespaceID:  1,
		DeploymentID: 1,
		StatusID:     1,
	}

	err := suite.repo.Insert(service)
	suite.Require().NoError(err)

	// Update the is_active field
	service.IsActive = false
	service.Name = "updated-service"

	err = suite.repo.Update(service)
	assert.NoError(suite.T(), err)

	// Verify the update
	var updatedService domain.Service
	err = suite.db.First(&updatedService, service.ID).Error
	assert.NoError(suite.T(), err)
	assert.False(suite.T(), updatedService.IsActive)
	assert.Equal(suite.T(), "updated-service", updatedService.Name)
}

func (suite *ServiceRepositoryTestSuite) TestFindByID() {
	// Insert a service
	service := &domain.Service{
		Name:         "test-service",
		Port:         "8080",
		TargetPort:   "8080",
		Type:         "ClusterIP",
		IsActive:     false,
		NamespaceID:  1,
		DeploymentID: 1,
		StatusID:     1,
	}

	err := suite.repo.Insert(service)
	suite.Require().NoError(err)

	// Find by ID
	foundService, err := suite.repo.FindByID(service.ID)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), foundService)
	assert.Equal(suite.T(), service.ID, foundService.ID)
	assert.Equal(suite.T(), "test-service", foundService.Name)
	assert.False(suite.T(), foundService.IsActive)
}

func TestServiceRepositoryTestSuite(t *testing.T) {
	suite.Run(t, new(ServiceRepositoryTestSuite))
}
